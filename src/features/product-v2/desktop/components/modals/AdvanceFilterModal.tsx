import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'

import CloseIcon from '@/assets/icons/exit.svg'
import ArrowRight from '@/assets/icons/arrow-right-primary.svg'
import SearchIcon from '@/assets/icons/search-normal.svg'

import { useState } from 'react'
import { useGetProductAgeGroup } from '@/features/product-v2/hooks/query/useGetProductAgeGroup'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useGetMedicineType } from '@/features/product-v2/hooks/query/useGetMedicineType'
import { But<PERSON> } from '@/components/ui/Button/Button'
import { PRODUCT_V2_TYPE_OPTIONS } from '@/features/product-v2/constants'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import { useGetCategoriesV2 } from '@/features/product-v2/hooks/query/useGetCategoriesV2'
import InfiniteScroll from 'react-infinite-scroll-component'
import { Spinner } from '@/components/ui/Loading/Spinner'
import { Input } from '@/components/ui/Input/Input'

const AdvanceFilterModal: React.FC = () => {
  const t = useTranslations()

  const locale = useLocale()

  const [selectedCategories, setSelectedCategories] = useState<Record<string, any>>({})

  const [selectProjectType, setSelectProjectType] = useState<ProductV2TypeEnum>(
    ProductV2TypeEnum.MEDICINE,
  )
  const [isViewAll, setIsViewAll] = useState<boolean>(false)

  const { productAgeGroups, isGetProductAgeGroupLoading } = useGetProductAgeGroup({
    params: {
      depth: 1,
      limit: 20,
      locale: locale ?? 'vi',
    },

    config: {
      staleTime: 5 * 60 * 1000,
    },
  })

  const { medicineType, isGetMedicineTypeLoading } = useGetMedicineType({
    params: {
      depth: 1,
      limit: 0,
      locale: locale ?? 'vi',
    },

    config: {
      staleTime: 5 * 60 * 1000,
      enabled: true,
    },
  })

  const { categoriesV2, isLoading, fetchNextPage, hasNextPage } = useGetCategoriesV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      ...(isViewAll ? { limit: 30 } : { limit: 6 }),
      depth: 1,
      locale: locale ?? 'vi',
      select: {
        id: true,
        title: true,
        icon: true,
      },
    },
  })

  const handleSelectCategory = (category: any, type: 'medicine' | 'age' | 'remove') => {
    setSelectedCategories((prev) => {
      const newSelected = { ...prev }
      if (newSelected[category.id]) {
        delete newSelected[category.id]
      } else {
        newSelected[category.id] = {
          ...category,
          type,
        }
      }
      return newSelected
    })
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {}

  const handleSelectProductType = (type: string) => {}

  const handleApplyFilter = (type: 'default' | 'filter') => {
    const categoriesSelected = Object.values(selectedCategories)

    // applyFilter({ params: type === 'filter' ? categoriesSelected : [], type })
  }

  return (
    <div className="flex flex-col gap-3 bg-white p-6">
      <div className="flex items-center justify-between py-2">
        <span className="typo-heading-7 text-primary-500">{t('MES-743')}</span>

        <Image onClick={close} className="size-6 cursor-pointer" src={CloseIcon} alt={'close'} />
      </div>

      {isViewAll ? (
        <div className="flex flex-1 flex-col gap-3 overflow-auto">
          <div className="relative w-full">
            <Input
              placeholder={t('MES-66')}
              className="w-full rounded-lg px-4 py-2 pr-6"
              onChange={handleInputChange}
            />
            <Image
              alt={'search'}
              src={SearchIcon}
              width={20}
              height={20}
              className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
            />
          </div>
          {/* Danh mục chi tiết All with Infinite Scroll */}
          {categoriesV2 && categoriesV2.pages[0]?.docs.length > 0 ? (
            <InfiniteScroll
              dataLength={categoriesV2.pages.flatMap((page) => page.docs).length}
              next={() => {
                if (hasNextPage) {
                  fetchNextPage()
                }
              }}
              hasMore={!!hasNextPage}
              loader={
                <div className="flex items-center justify-center py-4">
                  <Spinner />
                </div>
              }
              scrollThreshold={0.8}
              className="mt-4"
            >
              <div className="grid grid-cols-3 gap-3">
                {categoriesV2.pages.map((group, pageIndex) =>
                  group.docs.map((medicineType) => (
                    <div
                      onClick={() => handleSelectCategory(medicineType, 'medicine')}
                      key={`infinite-${medicineType.id}-${pageIndex}`}
                      className={`typo-body-6 flex min-h-14 cursor-pointer items-center justify-center rounded-lg border ${selectedCategories[medicineType.id] ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-200'}`}
                    >
                      {medicineType.title}
                    </div>
                  )),
                )}
              </div>
            </InfiniteScroll>
          ) : isLoading ? (
            <div className="mt-4 grid grid-cols-3 gap-3">
              {Array.from({ length: 3 }).map((_, idx) => (
                <Skeleton key={idx} className="min-h-[66px]"></Skeleton>
              ))}
            </div>
          ) : null}
        </div>
      ) : (
        <div className="flex flex-1 flex-col gap-3 overflow-auto">
          <div className="flex flex-wrap items-center gap-3">
            <span className="typo-body-7 text-subdued">
              {t('MES-706')} ({Object.keys(selectedCategories).length}):
            </span>
            <div className="flex flex-wrap items-center gap-2">
              {Object.values(selectedCategories).map((category) => (
                <div
                  key={category.id}
                  className="flex items-center gap-3 rounded-md border border-primary-500 px-2 py-1 text-primary-500"
                >
                  {category.title}
                  <Image
                    className="cursor-pointer"
                    src={CloseIcon}
                    alt="remove"
                    height={16}
                    width={16}
                    onClick={() => handleSelectCategory(category, 'remove')}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Nhóm sản phẩm */}
          <div className="grid grid-cols-3 gap-3">
            {Object.values(PRODUCT_V2_TYPE_OPTIONS).map((product) => (
              <div
                onClick={() => handleSelectProductType(product.value)}
                key={product.label}
                className={`typo-body-6 flex cursor-pointer items-center justify-center rounded-xl border ${product.value === selectProjectType ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-200'} min-h-14`}
              >
                {t(product.translationKey)}
              </div>
            ))}
          </div>

          {/* Danh mục chi tiết */}
          <div className="">
            <div className="flex items-center justify-between gap-3">
              <div className="typo-body-3">{t('MES-790')}</div>
              {!!categoriesV2?.pages[0]?.hasNextPage && (
                <div
                  onClick={() => setIsViewAll(true)}
                  className="typo-body-6 flex cursor-pointer items-center gap-2 text-primary-500"
                >
                  {t('MES-141')}
                  <Image src={ArrowRight} alt={'arrow right'} className="size-4" />
                </div>
              )}
            </div>

            <div className="mt-4 grid grid-cols-3 gap-3">
              {isLoading
                ? Array.from({ length: 3 }).map((_, idx) => (
                    <Skeleton key={idx} className="min-h-[66px]"></Skeleton>
                  ))
                : categoriesV2?.pages[0]?.docs?.map((medicineType) => (
                    <div
                      onClick={() => handleSelectCategory(medicineType, 'medicine')}
                      key={medicineType.id}
                      className={`typo-body-6 flex min-h-14 cursor-pointer items-center justify-center rounded-lg border ${selectedCategories[medicineType.id] ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-200'}`}
                    >
                      {medicineType.title}
                    </div>
                  ))}
            </div>
          </div>

          {/* Độ tuổi sử dụng */}
          <div className="">
            <div className="typo-body-3">{t('MES-707')}</div>

            <div className="mt-4 grid grid-cols-3 gap-3">
              {isGetProductAgeGroupLoading
                ? Array.from({ length: 3 }).map((_, idx) => (
                    <Skeleton key={idx} className="min-h-[66px]"></Skeleton>
                  ))
                : productAgeGroups?.docs?.map((ageGroup) => (
                    <div
                      onClick={() => handleSelectCategory(ageGroup, 'age')}
                      key={ageGroup.id}
                      className={`typo-body-6 flex min-h-14 cursor-pointer items-center justify-center rounded-lg border ${selectedCategories[ageGroup.id] ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-200'}`}
                    >
                      {ageGroup.title}
                    </div>
                  ))}
            </div>
          </div>

          {/* Loại sản phẩm */}
          <div className="">
            <div className="typo-body-3">{t('MES-708')}</div>

            <div className="mt-4 grid grid-cols-3 gap-3">
              {isGetMedicineTypeLoading
                ? Array.from({ length: 3 }).map((_, idx) => (
                    <Skeleton key={idx} className="min-h-[66px]"></Skeleton>
                  ))
                : medicineType?.docs?.map((medicineType) => (
                    <div
                      onClick={() => handleSelectCategory(medicineType, 'medicine')}
                      key={medicineType.id}
                      className={`typo-body-6 $ flex min-h-14 cursor-pointer items-center justify-center rounded-lg border ${selectedCategories[medicineType.id] ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-200'}`}
                    >
                      {medicineType.title}
                    </div>
                  ))}
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-end gap-3 pt-6">
        <Button
          onClick={() => handleApplyFilter('default')}
          variant={'outline'}
          className="typo-button-3 border-custom-neutral-200 px-4 py-2"
        >
          {t('MES-105')}
        </Button>
        <Button
          onClick={() => handleApplyFilter('filter')}
          variant={'default'}
          className="typo-button-3 px-4 py-2"
        >
          {t('MES-281')}
        </Button>
      </div>
    </div>
  )
}

export default AdvanceFilterModal
