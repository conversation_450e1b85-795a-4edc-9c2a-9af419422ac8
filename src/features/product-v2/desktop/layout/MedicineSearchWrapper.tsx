'use client'
import { useLocale } from 'next-intl'
import { useGetInfiniteProductsV2 } from '../../hooks/query/useGetInfiniteProductsV2'
import SearchProduct from '../common/SearchProduct'
import { useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { Spinner } from '@/components/ui/Loading/Spinner'
import ProductSearchList from '../components/products/ProductSearchList'

type MedicineSearchWrapperProps = {
  paramsSearch: {
    [key: string]: string | undefined
  }
}
const MedicineSearchWrapper: React.FC<MedicineSearchWrapperProps> = () => {
  const locale = useLocale()

  const [filterOptions, setFilterOptions] = useState<any>([])
  const handleChange = (value: string) => {
    console.log(value)
  }

  const {
    isLoading: isProductListLoading,
    productsV2: productList,
    fetchNextPage,
    hasNextPage,
  } = useGetInfiniteProductsV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: locale ?? 'vi',
      limit: 30,
    },
  })

  const allProductListData = productList?.pages.flatMap((page) => page.docs) ?? []

  const applyFilter = (data: any) => {
    const { params } = data
    setFilterOptions(params)
  }

  return (
    <div className="h-full w-full max-w-[calc(100vw-320px)] bg-custom-background-hover px-16 py-6">
      <div className="w-full rounded-xl bg-white p-4">
        <SearchProduct onChange={handleChange} />

        {/* Product List with Infinite Scroll */}
        {isProductListLoading && allProductListData.length === 0 ? (
          <ProductSearchList
            totalData={productList?.pages[0]?.totalDocs ?? 0}
            isLoading={true}
            productListData={[]}
            applyFilter={applyFilter}
            filterOptions={filterOptions}
          />
        ) : (
          productList &&
          productList.pages[0]?.docs.length > 0 && (
            <InfiniteScroll
              dataLength={allProductListData.length}
              next={() => {
                if (hasNextPage) {
                  fetchNextPage()
                }
              }}
              hasMore={!!hasNextPage}
              loader={
                <div className="flex items-center justify-center py-4">
                  <Spinner />
                </div>
              }
              scrollThreshold={0.8}
            >
              <ProductSearchList
                totalData={productList?.pages[0]?.totalDocs ?? 0}
                isLoading={false}
                productListData={allProductListData}
                filterOptions={filterOptions}
                applyFilter={applyFilter}
              />
            </InfiniteScroll>
          )
        )}
      </div>
    </div>
  )
}

export default MedicineSearchWrapper
