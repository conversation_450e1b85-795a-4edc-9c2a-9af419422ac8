'use client'

import { Input } from '@/components/ui/Input/Input'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { useRef, useState, useEffect } from 'react'

// image
import SearchIcon from '@/assets/icons/search-normal.svg'
import CircleClose from '@/assets/icons/close-circle-gray-filled.svg'
import CameraIcon from '@/assets/icons/camera-icon.svg'
import { Button } from '@/components/ui/Button/Button'
import { PRODUCT_V2_TYPE_OPTIONS } from '@/features/product-v2/constants'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { useDebounce } from '@/utilities/useDebounce'
import { useRouter } from 'next/navigation'
import SearchProduct from '../common/SearchProduct'

const MedicineHeader: React.FC = ({}) => {
  const t = useTranslations()
  const isInitializedRef = useRef<boolean>(false)

  const router = useRouter()

  const { updateSearchQuery, getAllSearchQueries } = useSearchQuery()

  const [inputValue, setInputValue] = useState<string>('')

  const [selectedCategories, setSelectedCategories] = useState<ProductV2TypeEnum>(
    ProductV2TypeEnum.MEDICINE,
  )

  const debouncedSearchValue = useDebounce(inputValue, 500)

  const { category } = getAllSearchQueries()
  // Initialize category from URL on component mount
  useEffect(() => {
    if (category && Object.values(ProductV2TypeEnum).includes(category as ProductV2TypeEnum)) {
      setSelectedCategories(category as ProductV2TypeEnum)
    }
  }, [category])

  useEffect(() => {
    if (!isInitializedRef.current) {
      return
    }

    router.push(`/desktop/medicines/search?q=${debouncedSearchValue}`)
  }, [debouncedSearchValue, router])

  const handleInputChange = (data: string) => {
    isInitializedRef.current = true

    setInputValue(data)
  }

  const handleSelectCategory = (category: ProductV2TypeEnum) => {
    if (selectedCategories === category) return
    setSelectedCategories(category)
    updateSearchQuery({ category }, 'replace', true, { reset: true })
  }

  return (
    <>
      <div className="flex items-center justify-between gap-3">
        <div className="typo-heading-7 text-primary-500">{t('MES-561')}</div>

        <SearchProduct onChange={handleInputChange} />
      </div>

      <div className="mt-3 flex items-center gap-3">
        {Object.values(PRODUCT_V2_TYPE_OPTIONS).map((product) => (
          <div
            onClick={() => handleSelectCategory(product.value)}
            key={product.label}
            className={`typo-body-6 cursor-pointer rounded-xl border ${product.value === selectedCategories ? 'border-primary-500 bg-white text-primary-500' : 'border-transparent bg-neutral-50 text-subdued'} px-3 py-[6px]`}
          >
            {t(product.translationKey)}
          </div>
        ))}
      </div>
    </>
  )
}

export default MedicineHeader
